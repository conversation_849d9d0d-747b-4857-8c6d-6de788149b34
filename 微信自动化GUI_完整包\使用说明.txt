# 微信自动化GUI程序

## 程序说明
这是一个微信自动化添加好友的GUI程序，支持批量添加微信好友。

## 使用方法

### 1. 准备工作
- 确保微信PC版已安装并登录
- 准备好友名单Excel文件（添加好友名单.xlsx）
- 检查配置文件设置

### 2. 启动程序
双击运行 `微信自动化GUI.exe` 启动程序

### 3. 配置设置
- 在程序界面中选择Excel文件路径
- 调整自动化参数（延迟时间、重试次数等）
- 配置窗口管理选项

### 4. 开始执行
- 点击"开始自动化"按钮
- 程序会自动处理微信好友添加流程
- 可以随时点击"停止"按钮中断执行

## 文件说明

### 主程序
- `微信自动化GUI.exe`: 主程序可执行文件

### 配置文件
- `config.json`: 主配置文件
- `config_backup.json`: 配置备份文件
- `window_safety_config.json`: 窗口安全配置
- `添加好友名单.xlsx`: 好友名单Excel文件

### 目录说明
- `logs/`: 程序运行日志
- `results/`: 执行结果文件
- `screenshots/`: 截图文件

## 注意事项

1. **系统要求**
   - Windows 10/11 操作系统
   - 微信PC版 (建议最新版本)
   - 屏幕分辨率建议 1920x1080 或更高

2. **使用建议**
   - 首次使用建议先测试少量联系人
   - 注意微信的频率限制，避免操作过于频繁
   - 定期备份配置文件和数据

3. **故障排除**
   - 如果程序无法启动，检查是否缺少必要的配置文件
   - 如果自动化失败，检查微信窗口是否正常显示
   - 查看logs目录中的日志文件获取详细错误信息

## 技术支持
如有问题，请查看日志文件或联系技术支持。

---
打包时间: 2025-08-04 08:31:11
程序版本: 1.0.0
